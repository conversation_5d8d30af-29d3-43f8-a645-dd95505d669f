package com.yyigou.ddc.services.dsrp.gsp.manager.qcNew.rule;


import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.persistent.mybatis.plugin.annotation.EnableDTOCheck;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.services.ddc.csc.vo.ConfigureVo;
import com.yyigou.ddc.services.ddc.uim.enums.TenantOrgFlagEnum;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationExtandVo;
import com.yyigou.ddc.services.dlog.util.DLogUtil;
import com.yyigou.ddc.services.dlog.vo.DLogVo;
import com.yyigou.ddc.services.dsrp.gsp.dto.QualificationControlBillSetActionQueryDto;
import com.yyigou.ddc.services.dsrp.gsp.dto.QualificationControlBillSetActionSaveDto;
import com.yyigou.ddc.services.dsrp.gsp.dto.QualificationControlBillSetQueryDto;
import com.yyigou.ddc.services.dsrp.gsp.dto.QualificationControlBillSetSaveDto;
import com.yyigou.ddc.services.dsrp.gsp.dto.qcNew.*;
import com.yyigou.ddc.services.dsrp.gsp.enums.GSPActionCodeEnum;
import com.yyigou.ddc.services.dsrp.gsp.enums.GSPLogOperateTypeEnum;
import com.yyigou.ddc.services.dsrp.gsp.pojo.QualificationControlBillSet;
import com.yyigou.ddc.services.dsrp.gsp.pojo.QualificationControlBillSetAction;
import com.yyigou.ddc.services.dsrp.gsp.util.CommonUtil;
import com.yyigou.ddc.services.dsrp.gsp.vo.QualificationControlBillSetLogVo;
import com.yyigou.ddc.services.dsrp.gsp.vo.qcNew.*;
import com.yyigou.ddc.services.dsrp.pc.enums.DeletedEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * gps校验规则管理 (多组织升级版本)
 * 1. BillSet表新增org_no的支持
 * 2. 新的管理页面/查询页面的支持
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class QcBillSetManager extends BillSetBase {
    /**
     * 批量保存校验规则
     *
     * @param batchSaveDto
     * @param sessionUser
     * @return
     */
    @EnableDTOCheck(requiredFields = {"orgNo", "gspCheckEnabled"})
    @Transactional
    public QcBillSetBatchSaveVo saveOrgRulesBatch(QcBillSetBatchSaveDto batchSaveDto, SessionUser sessionUser) {
        if (log.isInfoEnabled()) {
            log.info("saveOrgRulesBatch, batchSaveDto:{}", JSONUtil.toJsonPrettyStr(batchSaveDto));
        }
        String enterpriseNo = sessionUser.getEnterpriseNo();
        String orgNo = batchSaveDto.getOrgNo(); // 组织, 必填
        String funcNo = getPageFuncNo(sessionUser); // 菜单, 必填
        Boolean gspCheckEnabled = batchSaveDto.getGspCheckEnabled(); // 是否启用gsp校验, 必填
        // 返回结果
        QcBillSetBatchSaveVo resultVo = new QcBillSetBatchSaveVo();
        resultVo.setEnterpriseNo(enterpriseNo);
        resultVo.setOrgNo(orgNo);

        if (BooleanUtil.isTrue(gspCheckEnabled)) { // 启用gsp校验
            List<QualificationControlBillSetSaveDto> batchSaveDtoList = batchSaveDto.getBatchSaveDtoList();
            // 批量保存组织级校验规则, 组织与菜单上下文必填
            // 校验会话用户的组织权限
            uimUtil.checkOrgPrivilege(orgNo, funcNo, sessionUser);
            // 清理当前组织的所有校验规则, 动作
            QualificationControlBillSetQueryDto queryDto = new QualificationControlBillSetQueryDto();
            queryDto.setOrgNo(orgNo);
            QcBillSetQueryVo queryVo = findOrgRuleList(queryDto, sessionUser);
            List<QcBillSetVo> rules = queryVo.getRules();
            if (!CollectionUtils.isEmpty(rules)) {
                List<Long> setIds = rules.stream().map(QcBillSetVo::getId).collect(Collectors.toList());
                deleteRulesByIds(setIds, sessionUser);
            }
            // 全新批量新增
            boolean added = false;
            for (QualificationControlBillSetSaveDto saveDto : batchSaveDtoList) {
                saveDto.setOrgNo(orgNo);
                saveOrgRule(saveDto, sessionUser);
                added = true;
            }

            String enabled;
            if (CollectionUtils.isEmpty(batchSaveDtoList) && checkAnyEnterpriseRules(sessionUser)) {  // 如果没有组织级规则, 但是有租户
                enabled = "1";
                log.warn("组织没有规则, 但是有租户级的, 仍然开启gsp选项");
            } else if (CollectionUtils.isEmpty(batchSaveDtoList) && !checkAnyEnterpriseRules(sessionUser)) { // 如果没有组织级规则, 也没有租户级
                throw new BusinessException("不存在租户级校验规则时, 必须指定组织级规则才能启用GSP管控");
            } else {
                enabled = added ? "1" : "0";
            }
            // 更新gsp管控参数
            cscConfigureUtil.updateOrgConfig(enterpriseNo, ORG_CONFIG_KEY, orgNo, enabled);
            resultVo.setGspCheckEnabled(enabled.equals("1"));

            // 处理业务日志
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    saveBusinessLogs(enterpriseNo, orgNo, GSPLogOperateTypeEnum.BATCH_SAVE.getName() + "资质校验规则", enabled.equals("1") ? "GSP管控启用" : "GSP管控关闭", sessionUser);
                }
            });
        } else {// 停用gsp校验, 仅停用, 不处理规则列表
            cscConfigureUtil.updateOrgConfig(enterpriseNo, ORG_CONFIG_KEY, orgNo, "0");
            resultVo.setGspCheckEnabled(Boolean.FALSE);

            // 处理业务日志
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    saveBusinessLogs(enterpriseNo, orgNo, GSPLogOperateTypeEnum.BATCH_SAVE.getName() + "资质校验规则", "GSP管控关闭", sessionUser);
                }
            });

        }

        return resultVo;
    }

    /**
     * 批量新增租户级校验规则
     * <p>
     * 业务细节
     * 1. 关闭租户级gsp校验选项, 需检查当前租户的组织启用的gsp校验选项, 如果有启用的, 且关闭租户级导致其无规则可用, 则无法关闭
     *
     * @param batchSaveDto
     * @param sessionUser
     * @return
     */
    @EnableDTOCheck(requiredFields = {"gspCheckEnabled"})
    @Transactional
    public QcBillSetBatchSaveVo saveEnterpriseRulesBatch(QcBillSetBatchSaveDto batchSaveDto, SessionUser sessionUser) {
        if (log.isInfoEnabled()) {
            log.info("saveEnterpriseRulesBatch, batchSaveDto:{}", JSONUtil.toJsonPrettyStr(batchSaveDto));
        }
        String enterpriseNo = sessionUser.getEnterpriseNo();
        Boolean gspCheckEnabled = batchSaveDto.getGspCheckEnabled(); // 是否启用gsp校验, 必填

        // 多组织场景, 不需要设置租户级gsp管控参数, 单组织才需要; 多组织场景主要使用组织级gsp参数, 设置了也不影响
        List<QualificationControlBillSetSaveDto> batchSaveDtoList = batchSaveDto.getBatchSaveDtoList();
        QcBillSetBatchSaveVo resultVo = new QcBillSetBatchSaveVo();
        resultVo.setEnterpriseNo(enterpriseNo);
        if (BooleanUtil.isTrue(gspCheckEnabled)) { // 启用gsp校验
            resultVo.setEnterpriseNo(enterpriseNo);
            // 批量删除租户级校验规则
            QualificationControlBillSetQueryDto queryDto = new QualificationControlBillSetQueryDto();
            QcBillSetQueryVo queryVo = findEnterpriseRuleList(queryDto, sessionUser);
            List<QcBillSetVo> rules = queryVo.getRules();
            if (!CollectionUtils.isEmpty(rules)) {
                List<Long> setIds = rules.stream().map(QcBillSetVo::getId).collect(Collectors.toList());
                deleteRulesByIds(setIds, sessionUser);
            }
            // 全新批量新增
            boolean added = false;
            for (QualificationControlBillSetSaveDto saveDto : batchSaveDtoList) {
                saveEnterpriseRule(saveDto, sessionUser);
                added = true;
            }
            // 如果没有租户级规则了, 本地事务还没提交, 校验本次变更租户级的规则, 是否会导致任何启用gsp管控的组织没有规则可用, 如果是本次回滚
            if (!added) {
                Integer tenantOrgFlag = sessionUser.getTenantOrgFlag();
                if (tenantOrgFlag.equals(TenantOrgFlagEnum.MULTI_ORG.getValue())) { // 多组织场景, 允许不配置租户级校验规则, 可以组织自己配置组织级
                    checkAnyOrgEnabledGspHasNoRulesAvailable(enterpriseNo);
                } else { // 单组织场景, 至少一个校验规则
                    throw new BusinessException("启用GSP管控, 至少配置一条资质校验规则");
                }
            }
            // 更新gsp管控参数
            String enabled = added ? "1" : "0";
            cscConfigureUtil.updateEnterpriseConfig(enterpriseNo, ENTERPRISE_CONFIG_KEY, enabled);
            resultVo.setGspCheckEnabled(enabled.equals("1"));

            // 处理业务日志
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    saveBusinessLogs(enterpriseNo, null, GSPLogOperateTypeEnum.BATCH_SAVE.getName() + "资质校验规则", enabled.equals("1") ? "GSP管控启用" : "GSP管控关闭", sessionUser);
                }
            });
        } else {
            // 停用gsp校验, 仅停用, 不处理租户级规则列表
            cscConfigureUtil.updateEnterpriseConfig(enterpriseNo, ENTERPRISE_CONFIG_KEY, "0");
            resultVo.setGspCheckEnabled(Boolean.FALSE);

            // 处理业务日志
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    saveBusinessLogs(enterpriseNo, null, GSPLogOperateTypeEnum.BATCH_SAVE.getName() + "资质校验规则", "GSP管控关闭", sessionUser);
                }
            });
        }
        return resultVo;
    }

    /**
     * 校验是否有启用gsp管控的组织, 没有规则可用
     * 1. 本校验循环组织列表, 可能有性能问题, 所以遇到不满足就返回, 不再继续循环
     *
     * @param enterpriseNo 租户
     * @return
     */
    private void checkAnyOrgEnabledGspHasNoRulesAvailable(String enterpriseNo) {
        Map<String, String> orgNameMap = uimUtil.getEnterpriseOrgNameMap(enterpriseNo);
        // 存在gsp开启的组织
        List<String> enabledOrgNos = getAllGspEnabledOrgNos(enterpriseNo);
        if (CollectionUtils.isEmpty(enabledOrgNos)) {
            return;
        }
        // 检查有规则的组织
        QualificationControlBillSetQueryDto setQueryDto = new QualificationControlBillSetQueryDto();
        setQueryDto.setEnterpriseNo(enterpriseNo);  // 租户
        setQueryDto.setIncludeOrgNoList(enabledOrgNos); // 组织范围
        List<QualificationControlBillSet> orgRules = qualificationControlBillSetService.findList(setQueryDto);
        List<String> orgNosHasRules = orgRules.stream().map(QualificationControlBillSet::getOrgNo).distinct().collect(Collectors.toList());

        enabledOrgNos.removeAll(orgNosHasRules);// 反选, 剩下的组织有风险
        // 校验处理
        if (!CollectionUtils.isEmpty(enabledOrgNos)) {
            List<String> issueOrgNames = Lists.newArrayList();
            for (String issueOrgNo : enabledOrgNos) {
                issueOrgNames.add(orgNameMap.get(issueOrgNo));
            }
            String msg = String.format("存在以下组织使用到租户级规则，无法全部删除。组织名称【%s】", Joiner.on(",").join(issueOrgNames));
            throw new BusinessException(msg);
        }
    }

    /**
     * 租户是否存在租户级规则
     *
     * @return true:存在, false:不存在
     */
    private boolean checkAnyEnterpriseRules(SessionUser sessionUser) {
        QualificationControlBillSetQueryDto queryDto = new QualificationControlBillSetQueryDto();

        QcBillSetQueryVo queryVo = findEnterpriseRuleList(queryDto, sessionUser);

        if (queryVo.getGspCheckEnabled().equals(Boolean.FALSE)) {
            return false;
        }
        return !CollectionUtils.isEmpty(queryVo.getRules());

    }


    /**
     * 添加校验规则 (组织级)
     *
     * @param saveDto     新增入参
     * @param sessionUser 会话
     * @return 新增出参
     */
    @EnableDTOCheck(requiredFields = {"orgNo"})
    public QcBillSetVo saveOrgRule(QualificationControlBillSetSaveDto saveDto, SessionUser sessionUser) {
        if (log.isInfoEnabled()) {
            log.info("saveOrgRule, saveDto:{}", JSONUtil.toJsonPrettyStr(saveDto));
        }
        String enterpriseNo = sessionUser.getEnterpriseNo();
        String orgNo = saveDto.getOrgNo();
        saveDto.setEnterpriseNo(enterpriseNo);

        QualificationControlBillSet pojo = new QualificationControlBillSet();
        BeanUtils.copyProperties(saveDto, pojo);
        fillDefaultData(pojo, sessionUser, true);
        // 新增组织级规则 (先插入, 后面校验失败会回滚)
        pojo.setId(null);
        pojo = qualificationControlBillSetService.save(pojo);
        // 校验规则与
        List<QualificationControlBillSetActionSaveDto> setActionSaveDtoList = new ArrayList<>();
        saveDto.setId(pojo.getId());
        checkDtoData(saveDto, setActionSaveDtoList);
        // 新增规则动作
        QualificationControlBillSet finalPojo = pojo;
        List<QualificationControlBillSetAction> setActionList = setActionSaveDtoList.stream().map(p -> {
            QualificationControlBillSetAction qualificationControlBillSetAction = new QualificationControlBillSetAction();
            BeanUtils.copyProperties(p, qualificationControlBillSetAction);
            qualificationControlBillSetAction.setDeleted(DeletedEnum.UN_DELETE.getValue());
            qualificationControlBillSetAction.setBillSetId(finalPojo.getId());
            qualificationControlBillSetAction.setCreateNo(finalPojo.getCreateNo());
            qualificationControlBillSetAction.setCreateName(finalPojo.getCreateName());
            qualificationControlBillSetAction.setCreateTime(finalPojo.getCreateTime());
            return qualificationControlBillSetAction;
        }).collect(Collectors.toList());
        qualificationControlBillSetActionService.saveBatch(setActionList);
        //转换为VO
        QcBillSetVo qualificationControlBillSetVo = new QcBillSetVo();
        BeanUtils.copyProperties(pojo, qualificationControlBillSetVo);

        return qualificationControlBillSetVo;
    }

    /**
     * 添加校验规则 (多组织租户级 || 单组织)
     * (注意: 多组织的租户级, 单组织的校验规则orgNo都是空)
     *
     * @param saveDto     新增入参
     * @param sessionUser 会话
     * @return 新增出参
     */
    @EnableDTOCheck
    public QcBillSetVo saveEnterpriseRule(QualificationControlBillSetSaveDto saveDto, SessionUser sessionUser) {
        if (log.isInfoEnabled()) {
            log.info("saveEnterpriseRule, saveDto:{}", JSONUtil.toJsonPrettyStr(saveDto));
        }
        String enterpriseNo = sessionUser.getEnterpriseNo(); // 租户
        saveDto.setEnterpriseNo(enterpriseNo);
        List<QualificationControlBillSetActionSaveDto> setActionSaveDtoList = new ArrayList<>();

        QualificationControlBillSet pojo = new QualificationControlBillSet();
        BeanUtils.copyProperties(saveDto, pojo);
        pojo.setId(null);
        //补充对象默认属性
        fillDefaultData(pojo, sessionUser, true);
        pojo = qualificationControlBillSetService.save(pojo);
        //验证dto参数格式是否正确
        saveDto.setId(pojo.getId());
        checkDtoData(saveDto, setActionSaveDtoList);
        QualificationControlBillSet finalPojo = pojo;
        List<QualificationControlBillSetAction> setActionList = setActionSaveDtoList.stream().map(p -> {
            QualificationControlBillSetAction qualificationControlBillSetAction = new QualificationControlBillSetAction();
            BeanUtils.copyProperties(p, qualificationControlBillSetAction);
            qualificationControlBillSetAction.setDeleted(DeletedEnum.UN_DELETE.getValue());
            qualificationControlBillSetAction.setBillSetId(finalPojo.getId());
            qualificationControlBillSetAction.setCreateNo(finalPojo.getCreateNo());
            qualificationControlBillSetAction.setCreateName(finalPojo.getCreateName());
            qualificationControlBillSetAction.setCreateTime(finalPojo.getCreateTime());
            return qualificationControlBillSetAction;
        }).collect(Collectors.toList());
        qualificationControlBillSetActionService.saveBatch(setActionList);
        //转换为VO
        QcBillSetVo qualificationControlBillSetVo = new QcBillSetVo();
        BeanUtils.copyProperties(pojo, qualificationControlBillSetVo);
        return qualificationControlBillSetVo;
    }

    /**
     * 将选择组织的校验规则/gsp管控参数, 复制给指定的其他组织
     *
     * @param syncDto     同步入参
     * @param sessionUser 会话
     * @return 同步结果
     */
    @EnableDTOCheck(requiredFields = {"orgNo", "syncOrgNos"})
    @Transactional
    public List<QcBillSetBatchSaveVo> saveSyncOrgRulesBatch(QcBillSetBatchSyncDto syncDto, SessionUser sessionUser) {
        if (log.isInfoEnabled()) {
            log.info("saveSyncOrgRulesBatch, syncDto:{}", JSONUtil.toJsonPrettyStr(syncDto));
        }
        String enterpriseNo = sessionUser.getEnterpriseNo();
        String funcNo = getPageFuncNo(sessionUser); // 菜单编号, 必填
        String orgNo = syncDto.getOrgNo(); // 同步组织源头, 必填
        List<String> syncOrgNos = syncDto.getSyncOrgNos(); // 同步组织目标, 必填
        List<QualificationControlBillSetSaveDto> batchSaveDtoList = syncDto.getBatchSaveDtoList(); // 来源组织的规则列表
        // 校验同步组织
        syncOrgNos = syncOrgNos.stream().distinct().collect(Collectors.toList());
        syncOrgNos.remove(orgNo); // 踢掉来源组织
        if (CollectionUtils.isEmpty(syncOrgNos)) {
            throw new BusinessException("同步的目标组织范围不合法");
        }
        // 校验组织是否在授权范围内
        List<OrganizationExtandVo> orgs = uimUtil.listAllAuthedOrgs(funcNo, sessionUser);
        List<String> authedOrgNos = orgs.stream().map(OrganizationExtandVo::getOrgNo).collect(Collectors.toList());
        if (!new HashSet<>(authedOrgNos).containsAll(syncOrgNos)) {
            throw new BusinessException("同步的目标组织中, 存在不在用户的授权范围内的组织");
        }
        // 读取来源组织的gsp管控参数
        ConfigureVo gspCscConfigure = getGspCscConfigure(enterpriseNo, orgNo);
        boolean gspCheckEnabled = checkGSPCheckEnabled(gspCscConfigure);
        List<QcBillSetBatchSaveVo> result = new ArrayList<>();
        if (!gspCheckEnabled) { // gsp管控参数关闭时, 将目标组织的gsp管控参数关闭, 规则不动
            for (String syncOrgNo : syncOrgNos) {
                QcBillSetBatchSaveVo batchSaveVO = new QcBillSetBatchSaveVo();
                batchSaveVO.setEnterpriseNo(enterpriseNo);
                batchSaveVO.setOrgNo(syncOrgNo); // 修改组织编号
                cscConfigureUtil.updateOrgConfig(enterpriseNo, ORG_CONFIG_KEY, syncOrgNo, "0");
                batchSaveVO.setGspCheckEnabled(Boolean.FALSE);
                result.add(batchSaveVO);
            }
        } else { // 来源组织管控参数启用, 开始同步规则
            // 读取来源组织的gps管控规则并同步给目标组织
            QcBillSetBatchSaveDto batchSaveDto = new QcBillSetBatchSaveDto();
            batchSaveDto.setBatchSaveDtoList(batchSaveDtoList); // 规则

            // 遍历目标组织, 同步gsp管控规则与gsp管控参数
            for (String syncOrgNo : syncOrgNos) {
                batchSaveDto.setOrgNo(syncOrgNo);
                batchSaveDto.setGspCheckEnabled(true); // 启用gsp管控
                // 处理每个目标组织的返回
                QcBillSetBatchSaveVo batchSaveVO = new QcBillSetBatchSaveVo();
                batchSaveVO.setEnterpriseNo(enterpriseNo);
                batchSaveVO.setOrgNo(syncOrgNo); // 修改组织编号
                try {
                    batchSaveVO = saveOrgRulesBatch(batchSaveDto, sessionUser);
                } catch (Exception e) {
                    batchSaveVO.setMessage(e.getMessage());
                }
                result.add(batchSaveVO);
            }
        }

        return result;
    }


    /**
     * 校验规则多组织菜单左侧组织树
     *
     * @param sessionUser
     * @return
     */
    @EnableDTOCheck
    public List<OrganizationExtandVo> listAuthedOrgs(SessionUser sessionUser) {
        String funcNo = getPageFuncNo(sessionUser);
        return uimUtil.listAllAuthedOrgs(funcNo, sessionUser);
    }

    /**
     * 查询指定组织校验规则列表
     *
     * @param dto 查询参数
     */
    @EnableDTOCheck(requiredFields = {"orgNo"})
    public QcBillSetQueryVo findOrgRuleList(QualificationControlBillSetQueryDto dto, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        String orgNo = dto.getOrgNo();
        // 返回值封装
        QcBillSetQueryVo resultVo = new QcBillSetQueryVo();
        resultVo.setOrgNo(orgNo);
        // 查询管控参数配置
        ConfigureVo orgConfigVo = cscConfigureUtil.findOrgConfigList(enterpriseNo, ORG_CONFIG_KEY, orgNo);
        resultVo.setGspCheckEnabled(checkGSPCheckEnabled(orgConfigVo));

        // 查询规则并封装校验动作
        List<QcBillSetVo> ruleVos = queryAndFillRuleActions(dto, sessionUser);
        resultVo.setRules(ruleVos);
        return resultVo;
    }


    /**
     * 查询指定组织校验规则列表
     *
     * @param dto 查询参数
     */
    @EnableDTOCheck(requiredFields = {"enterpriseNo", "includeOrgNoList"})
    public Map<String, QcBillSetQueryVo> findOrgsRuleListNoSession(QualificationControlBillSetQueryDto dto) {
        String enterpriseNo = dto.getEnterpriseNo();
        List<String> includeOrgNoList = dto.getIncludeOrgNoList();

        // 查询规则并封装校验动作
        List<QcBillSetVo> ruleVos = queryAndFillRuleActionsNoSession(dto);
        if (CollectionUtils.isEmpty(ruleVos)) {
            return Collections.emptyMap();
        }

        Map<String, List<QcBillSetVo>> orgRuleMap = ruleVos.stream().collect(Collectors.groupingBy(QcBillSetVo::getOrgNo));

        Map<String, QcBillSetQueryVo> result = new HashMap<>();

        for (String orgNo : includeOrgNoList) {
            // 返回值封装
            QcBillSetQueryVo resultVo = new QcBillSetQueryVo();
            resultVo.setOrgNo(orgNo);
            // 查询管控参数配置
            ConfigureVo orgConfigVo = cscConfigureUtil.findOrgConfigList(enterpriseNo, ORG_CONFIG_KEY, orgNo);
            resultVo.setGspCheckEnabled(checkGSPCheckEnabled(orgConfigVo));

            resultVo.setRules(orgRuleMap.get(orgNo));

            result.put(orgNo, resultVo);
        }

        return result;
    }

    /**
     * 查询企业校验规则列表
     *
     * @param dto         入参
     * @param sessionUser 会话
     * @return 返回值
     */
    @EnableDTOCheck
    public QcBillSetQueryVo findEnterpriseRuleList(QualificationControlBillSetQueryDto dto, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        dto.setEnterpriseNo(enterpriseNo);
        // 返回值封装
        QcBillSetQueryVo resultVo = new QcBillSetQueryVo();
        // 查询管控参数配置
        ConfigureVo configureVo = cscConfigureUtil.findEnterpriseConfigList(enterpriseNo, ENTERPRISE_CONFIG_KEY);
        resultVo.setGspCheckEnabled(checkGSPCheckEnabled(configureVo));
        // 查询规则并封装校验动作
        List<QcBillSetVo> result = queryAndFillRuleActions(dto, sessionUser);

        resultVo.setRules(result);
        return resultVo;
    }

    /**
     * 资质校验规则组织查询: 列表接口
     * 1. 仅查询启用gsp管控参数的组织的规则
     * 2. 查询后, 可以批量设置来批改
     *
     * @param queryDto    规则校验查询对象
     * @param sessionUser 会话
     * @return 分页
     */
    @EnableDTOCheck
    public PageVo<QcBillSetVo> findOrgRulePageListOnQueryWeb(QualificationControlBillSetQueryDto queryDto, PageDto pageDto, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        // 查询组织级, 可设置查询条件为隐藏条件来做, 这里仅查询租户条件即可
        // 查询本企业启用gsp的组织
        List<String> enabledOrgNos = getAllGspEnabledOrgNos(enterpriseNo);
        queryDto.setEnterpriseNo(enterpriseNo);
        queryDto.setIncludeOrgNoList(enabledOrgNos); // 产品要求只要gsp管控参数启用的组织的规则查询, 这个查询方案里不好设置, 默认代码实现
        // 开始查询
        Page<QcBillSetVo> page = PageHelper.startPage(pageDto.getPageIndex(), pageDto.getPageSize());
        List<QcBillSetVo> qualificationControlBillSetList = queryAndFillRuleActions(queryDto, sessionUser);

        PageVo<QcBillSetVo> resultPageVo = new PageVo<>();
        resultPageVo.setPageCount(page.getPages());
        resultPageVo.setPageIndex(page.getPageNum());
        resultPageVo.setPageSize(page.getPageSize());
        resultPageVo.setTotal(page.getTotal());
        if (!CommonUtil.checkEmpty(page.getResult())) {
            resultPageVo.setRows(qualificationControlBillSetList);
        }
        return resultPageVo;
    }

    /**
     * 批量更新组织校验规则 (目前仅支持组织级规则的批量更新)
     * 1. 前端勾选组织级多个校验规则, 点击批量设置, 设置管控项的值进行批量更新
     * 2. 考虑性能, 应设置批改上限, 目前支持
     *
     * @param batchUpdateDto 批量设置对象
     * @param sessionUser    会话
     * @return 批量处理结果, 成功数量, 失败数量, 失败具体明细列表
     */
    @EnableDTOCheck(requiredFields = {"actionSetDto", "ruleIdList"})
    @Transactional
    public QcBillSetBatchUpdateVo batchUpdateOrgRules(QcBillSetBatchUpdateDto batchUpdateDto, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        QcBillSetActionSetDto actionSetDto = batchUpdateDto.getActionSetDto(); // 修改项的目标值配置
        List<Long> ruleIdList = batchUpdateDto.getRuleIdList(); // 勾选更新的校验规则列表, 空表示无需处理

        //校验批改范围, 不能超过200, 跟鲍伟伟沟通,说不限制, 暂时取消
        //        if(ruleIdList.size() > 200){
        //            throw new BusinessException("批量更新组织校验规则数量上限为200");
        //        }

        // 校验设置值是不是全部不管控 (如果管控项新增这里需要调整)
        QcBillSetBatchUpdateVo batchUpdateVo = new QcBillSetBatchUpdateVo();
        List<QcBillSetUpdateVo> failedList = Lists.newArrayList();
        batchUpdateVo.setFailedList(failedList);

        Map<String, String> orgNameMap = uimUtil.getEnterpriseOrgNameMap(enterpriseNo);

        for (Long ruleId : ruleIdList) {
            // 查询规则明细, 避免前端传入
            QcBillSetVo ruleVo = getRuleById(ruleId, sessionUser);
            String orgNo = ruleVo.getOrgNo();
            String billName = ruleVo.getBillName();
            try {
                // 当前仅支持组织级规则的更新
                if (StringUtils.isEmpty(orgNo)) {
                    String msg = String.format("单据【%s】的校验规则【%d】不是组织级, 不支持批量设置", billName, ruleId);
                    throw new BusinessException(msg);
                }
                // 修改前校验, 规则修改后, 管控项都是不管控
                // 将actionSetDto并入规则对象中
                int notControlItemCount = ruleVo.applySetDTOAndGetNotControlItemCount(actionSetDto);
                if (notControlItemCount == 5) {
                    String msg = String.format("组织【%s】的单据【%s】不能都设置为不管控。", orgNameMap.get(orgNo), billName);
                    throw new BusinessException(msg);
                }
                QualificationControlBillSet qualificationControlBillSet = new QualificationControlBillSet();
                BeanUtils.copyProperties(ruleVo, qualificationControlBillSet);
                qualificationControlBillSet.setId(ruleVo.getId());
                fillDefaultData(qualificationControlBillSet, sessionUser, false);
                // 更新规则配置
                qualificationControlBillSetService.update(qualificationControlBillSet);
                // 更新规则动作
                // 先查询当前的动作列表
                QualificationControlBillSetActionQueryDto actionQueryDto = new QualificationControlBillSetActionQueryDto();
                actionQueryDto.setEnterpriseNo(enterpriseNo);
                actionQueryDto.setBillSetId(ruleVo.getId());
                List<QualificationControlBillSetAction> actionPojoList = qualificationControlBillSetActionService.findList(actionQueryDto);

                for (QualificationControlBillSetAction actionPojo : actionPojoList) {
                    if (ruleVo.getFirstSaleRuleType() != null) {
                        actionPojo.setFirstSaleRuleType(ruleVo.getFirstSaleRuleType());
                    }
                    if (ruleVo.getCompleteRuleType() != null) {
                        actionPojo.setCompleteRuleType(ruleVo.getCompleteRuleType());
                    }
                    if (ruleVo.getGoodsExpiryRuleType() != null) {
                        actionPojo.setGoodsExpiryRuleType(ruleVo.getGoodsExpiryRuleType());
                    }
                    if (ruleVo.getCompanyExpiryRuleType() != null) {
                        actionPojo.setCompanyExpiryRuleType(ruleVo.getCompanyExpiryRuleType());
                    }
                    if (ruleVo.getBusinessRuleType() != null) {
                        actionPojo.setBusinessRuleType(ruleVo.getBusinessRuleType());
                    }
                    if (ruleVo.getIsCheckEnterprise() != null) {
                        actionPojo.setIsCheckEnterprise(ruleVo.getIsCheckEnterprise());
                    }
                    if (ruleVo.getIsGenerateCheckRecord() != null) {
                        actionPojo.setIsGenerateCheckRecord(ruleVo.getIsGenerateCheckRecord());
                    }
                    qualificationControlBillSetActionService.update(actionPojo);
                }

                // 保存业务日志
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        saveBusinessLogs(enterpriseNo, ruleVo.getOrgNo(), GSPLogOperateTypeEnum.BATCH_UPDATE.getName() + ruleVo.getBillName() + "资质校验规则", "GSP管控启用", sessionUser);
                    }
                });
            } catch (BusinessException e) {
                QcBillSetUpdateVo updateVo = new QcBillSetUpdateVo();
                updateVo.setFailReason(e.getMessage());
                updateVo.setUpdateStatus(false);
                updateVo.setRuleId(ruleId);
                failedList.add(updateVo);
            }
        }
        batchUpdateVo.setFailCount(failedList.size());
        batchUpdateVo.setSuccessCount(ruleIdList.size() - failedList.size());
        batchUpdateVo.setTotalCount(ruleIdList.size());
        return batchUpdateVo;
    }

    /**
     * 获取规则
     *
     * @param id 编号
     * @return QualificationControlBillSetDto
     */
    @EnableDTOCheck
    public QcBillSetVo getRuleById(Long id, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        // 先查询规则, 校验是否本企业
        QualificationControlBillSet oldPojo = qualificationControlBillSetService.getById(id);
        if (oldPojo == null || !enterpriseNo.equals(oldPojo.getEnterpriseNo())) {
            throw new BusinessException("校验规则不属于本租户");
        }
        // 填充动作信息
        List<QcBillSetVo> qualificationControlBillSetVos = fillRuleActions(enterpriseNo, Collections.singletonList(oldPojo));
        return qualificationControlBillSetVos.get(0);
    }

    /**
     * 批量删除对象
     *
     * @param ids
     * @param sessionUser
     */
    public void deleteRulesByIds(List<Long> ids, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        // 删除规则
        qualificationControlBillSetService.deleteByIds(enterpriseNo, ids);
        // 删除动作
        qualificationControlBillSetActionService.deleteByBillSetIds(enterpriseNo, ids);
    }


    /**
     * 校验规则配置查询 (组织级或租户级)
     *
     * @param params gsp校验配置查询入参
     * @return 1. null: 无需校验
     * 2. 非空: 有校验规则, 并将规则动作配置列表返回
     */
    @EnableDTOCheck(requiredFields = {"enterpriseNo", "orgNo", "billType"})
    public QcConfigResultVo findOrgGspConfigNoSession(QcConfigQueryDto params) {
        String enterpriseNo = params.getEnterpriseNo(); // 租户, 必填
        String billType = params.getBillType(); // 业务单据类型, 必填
        String orgNo = params.getOrgNo(); // 组织, 必填 (单组织/多组织都必填)
        /**********************
         * GSP管控参数检查      *
         *********************/
        boolean indEnterprise = uimUtil.isEnterpriseSingleOrg(enterpriseNo);
        boolean gspCheckEnabled = false;
        if (indEnterprise) { // 单组织场景,  其管控参数都是租户级 (租户级的规则org_no都是NULL)
            orgNo = null;
        }
        ConfigureVo gspCscConfigure = getGspCscConfigure(enterpriseNo, orgNo);
        gspCheckEnabled = checkGSPCheckEnabled(gspCscConfigure);
        /**********************
         * GSP资质校验规则查询   *
         *********************/
        // 返回值封装
        QcConfigResultVo qcConfigResultVo = new QcConfigResultVo();
        qcConfigResultVo.setIsConfig(false);
        qcConfigResultVo.setAuditActionIsConfig(false);
        qcConfigResultVo.setSubmitActionIsConfig(false);
        qcConfigResultVo.setEnterpriseNo(enterpriseNo);
        qcConfigResultVo.setOrgNo(orgNo);

        // 查询gsp管控参数
        if (!gspCheckEnabled) {
            // 未启用gsp管控, 直接返回
            qcConfigResultVo.setIsConfig(false);
            return qcConfigResultVo;
        }

        // 填充规则动作信息
        List<QualificationControlBillSet> billRules = getRulesInternal(enterpriseNo, orgNo, billType);

        if (CollectionUtils.isEmpty(billRules)) {
            // 没有规则, 直接返回
            qcConfigResultVo.setIsConfig(false);
            return qcConfigResultVo;
        }

        // 启用了GSP管控, 查询单据的校验规则
        qcConfigResultVo.setIsConfig(true);

        List<Long> ruleIds = billRules.stream().map(QualificationControlBillSet::getId).collect(Collectors.toList());
        // 根据单据类型、单据动作编码获取资质校验管控规则信息判断证照完整性、证照效期、经营范围是否配置管控
        QualificationControlBillSetActionQueryDto qualificationControlBillSetActionQueryDto = new QualificationControlBillSetActionQueryDto();
        qualificationControlBillSetActionQueryDto.setEnterpriseNo(enterpriseNo);
        qualificationControlBillSetActionQueryDto.setBillSetIdList(ruleIds);
        List<QualificationControlBillSetAction> ruleActions = qualificationControlBillSetActionService.findList(qualificationControlBillSetActionQueryDto);

        if (CollectionUtils.isEmpty(ruleActions)) {
            return qcConfigResultVo;
        }

        Map<String, QualificationControlBillSetAction> setActionMap = ruleActions.stream().collect(Collectors.toMap(p -> p.getActionCode(), p -> p));
        if (setActionMap.get(GSPActionCodeEnum.SUBMIT.getValue()) != null) {
            qcConfigResultVo.setSubmitActionIsConfig(true);
            QualificationControlBillSetAction ruleAction = setActionMap.get(GSPActionCodeEnum.SUBMIT.getValue());
            Boolean submitActionIsConfigForce = false;
            Integer force = new Integer(1);
            if (null != ruleAction.getFirstSaleRuleType() && force.equals(ruleAction.getFirstSaleRuleType())) {
                submitActionIsConfigForce = true;
            }
            if (null != ruleAction.getCompleteRuleType() && force.equals(ruleAction.getCompleteRuleType())) {
                submitActionIsConfigForce = true;
            }
            if (null != ruleAction.getGoodsExpiryRuleType() && force.equals(ruleAction.getGoodsExpiryRuleType())) {
                submitActionIsConfigForce = true;
            }
            if (null != ruleAction.getCompanyExpiryRuleType() && force.equals(ruleAction.getCompanyExpiryRuleType())) {
                submitActionIsConfigForce = true;
            }
            if (null != ruleAction.getBusinessRuleType() && force.equals(ruleAction.getBusinessRuleType())) {
                submitActionIsConfigForce = true;
            }
            qcConfigResultVo.setSubmitActionIsConfigForce(submitActionIsConfigForce);
            qcConfigResultVo.setSubmitActionRuleId(ruleAction.getBillSetId());
        }
        if (setActionMap.get(GSPActionCodeEnum.AUDIT.getValue()) != null) {
            qcConfigResultVo.setAuditActionIsConfig(true);
            QualificationControlBillSetAction ruleAction = setActionMap.get(GSPActionCodeEnum.AUDIT.getValue());
            Boolean auditActionIsConfigForce = false;
            Integer force = new Integer(1);
            if (null != ruleAction.getFirstSaleRuleType() && force.equals(ruleAction.getFirstSaleRuleType())) {
                auditActionIsConfigForce = true;
            }
            if (null != ruleAction.getCompleteRuleType() && force.equals(ruleAction.getCompleteRuleType())) {
                auditActionIsConfigForce = true;
            }
            if (null != ruleAction.getGoodsExpiryRuleType() && force.equals(ruleAction.getGoodsExpiryRuleType())) {
                auditActionIsConfigForce = true;
            }
            if (null != ruleAction.getCompanyExpiryRuleType() && force.equals(ruleAction.getCompanyExpiryRuleType())) {
                auditActionIsConfigForce = true;
            }
            if (null != ruleAction.getBusinessRuleType() && force.equals(ruleAction.getBusinessRuleType())) {
                auditActionIsConfigForce = true;
            }
            qcConfigResultVo.setAuditActionIsConfigForce(auditActionIsConfigForce);
            qcConfigResultVo.setAuditActionRuleId(ruleAction.getBillSetId());
        }
        return qcConfigResultVo;
    }

    /**
     * 查询业务日志
     *
     * @param orgNo       业务单元, 可为空, 用于单组织场景
     * @param pageDto     分页参数
     * @param sessionUser 会话
     * @return PageVo<DLogVo>
     */
    public PageVo<QualificationControlBillSetLogVo> findBillSetBusinessLogs(String orgNo, PageDto pageDto, SessionUser sessionUser) {
        String enterpriseNo = sessionUser.getEnterpriseNo();
        String businessKey = buildBusinessLogKey(enterpriseNo, orgNo);
        String businessType = getCurrentViewNo();
        PageVo<DLogVo> dLogVoPageVo = DLogUtil.pageRecentLogs(dLogAPI, businessKey, businessType, pageDto, sessionUser);
        List<QualificationControlBillSetLogVo> resultList = Lists.newArrayList();
        dLogVoPageVo.getRows().forEach(dLogVo -> {
            QualificationControlBillSetLogVo logVo = new QualificationControlBillSetLogVo();
            logVo.setOperateName(dLogVo.getUserName());
            logVo.setOperateNo(dLogVo.getUserId());
            logVo.setOperateTime(dLogVo.getCreateDate());
            logVo.setOperateContent(dLogVo.getBusinessContext());
            resultList.add(logVo);
        });

        PageVo<QualificationControlBillSetLogVo> resultPageVo = new PageVo<>();
        resultPageVo.setRows(resultList);
        resultPageVo.setTotal(dLogVoPageVo.getTotal());
        resultPageVo.setPageCount(dLogVoPageVo.getPageCount());
        resultPageVo.setPageIndex(dLogVoPageVo.getPageIndex());
        resultPageVo.setPageSize(dLogVoPageVo.getPageSize());
        return resultPageVo;
    }
}
